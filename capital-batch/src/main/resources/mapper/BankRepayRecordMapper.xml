<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.capital.batch.mapper.BankRepayRecordMapper">


    <select id="findSuccessList" resultType="com.jinghang.capital.batch.entity.BankRepayRecord">
        SELECT
        r.*
        FROM
        bank_repay_record r
        LEFT JOIN loan l ON r.loan_id = l.id
        WHERE
        <if test="channel != null">
            l.channel = #{channel}
        </if>
        <if test="guaranteeCompany != null">
            AND l.guarantee_company = #{guaranteeCompany}
        </if>
        <if test="repayType != null and repayType != ''">
            AND r.repay_type = #{repayType}
        </if>
        AND r.repay_status = 'SUCCESS'
        AND r.repay_time BETWEEN #{beginTime} AND #{endTime}
    </select>

    <select id="countFailBankRepayRecord" resultType="java.lang.Integer">
        SELECT COUNT(a.id)
        FROM (SELECT crr.*
              FROM customer_repay_record crr
              WHERE crr.repay_status = 'SUCCESS'
                AND crr.repay_mode = 'OFFLINE'
                AND crr.channel &lt;&gt; 'FBANK_FQL'
                AND crr.repay_time BETWEEN #{beginTime} AND #{endTime}) a
                 LEFT JOIN bank_repay_record brr ON a.loan_id = brr.loan_id
            AND a.period = brr.period
            AND brr.repay_mode = 'OFFLINE'
            AND brr.repay_status = 'SUCCESS'
        WHERE brr.id IS NULL;
    </select>

    <!-- 查询还款状态不为终态和还款时间小于等于计算后的时间的还款记录信息列表 -->
    <select id="findByRepayStatusAndCountBackTime" resultType="com.jinghang.capital.batch.entity.BankRepayRecord">
        select l.*
        from bank_repay_record l
        where l.repayStatus not in ('SUCCESS','FAIL')
        and l.repayTime &lt;= #{countBackTime}
    </select>

</mapper>
