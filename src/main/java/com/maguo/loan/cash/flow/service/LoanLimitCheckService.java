package com.maguo.loan.cash.flow.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.ProjectLimitConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.ProjectLimitConfigRepository;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.concurrent.TimeUnit;


@Service
public class LoanLimitCheckService {

    @Autowired
    private ProjectLimitConfigRepository projectLimitConfigRepository;
    @Autowired
    private LockService lockService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private CreditRepository creditRepository;

    private static final long LOCK_WAIT_SECOND = 5;
    private static final long LOCK_RELEASE_SECOND = 10;


    private static final String PREFIX_CREDIT = "CREDIT";

    private static final String PREFIX_LOAN = "LOAN";

    private static final Logger logger = LoggerFactory.getLogger(LoanCommonService.class);


    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public void checkLoanLimit(Order order) {
        Optional<Credit> creditOpt = creditRepository.findByOrderIdAndState(order.getId(),ProcessState.SUCCESS);
        if (creditOpt.isEmpty()) {
            logger.error("checkCreditLimit: [OrderId: {}], 无法找到对应的授信记录(Credit), 校验中止.", order.getId());
        }
        Credit credit = creditOpt.get();
        Loan loanForCheck = new Loan();
        loanForCheck.setId(order.getId());
        loanForCheck.setAmount(order.getApplyAmount());
        loanForCheck.setFlowChannel(credit.getFlowChannel());
        loanForCheck.setBankChannel(credit.getBankChannel());
        loanForCheck.setGuaranteeCompany(credit.getGuaranteeCompany());
        this.performSuspendCheck(loanForCheck);
    }

    public void checkLvXinCreditLimit(PreOrder preOrder) {
        logger.info("执行【授信】额度前置校验, PartnerUserId: {}", preOrder.getId());
        ProjectLimitConfig config = getLvXinProjectConfig(preOrder.getFlowChannel().toString(),
            GuaranteeCompany.CJRD.toString(), preOrder.getBankChannel().toString());
        logger.info("--数据库配置的授信金额------"+config.getCreditDayLimit());
        performLvXinCheck(config, preOrder.getApplyAmount(), PREFIX_CREDIT,preOrder.getOrderNo());
    }

    public void checkPaiPaiCreditLimit(PreOrder preOrder) {
        logger.info("执行【授信】额度前置校验, PartnerUserId: {}", preOrder.getId());
        ProjectLimitConfig config = getPaiPaiProjectConfig(preOrder.getFlowChannel().toString());
        performPaiPaiCheck(config, preOrder.getApplyAmount(), PREFIX_CREDIT,preOrder.getOrderNo());
    }

    private ProjectLimitConfig getLvXinProjectConfig(String flowChannel,String guaranteeCompany, String bankChannel) {
        return projectLimitConfigRepository.findByFlowChannelAndGuaranteeCompAndBankChannel(
            flowChannel,
            guaranteeCompany,
            bankChannel
        ).orElseThrow(() -> new BizException(ResultCode.NO_LIMIT_DAY_LOAN_FLOW_CHANNEL));
    }

    private ProjectLimitConfig getPaiPaiProjectConfig(String flowChannel) {
        return projectLimitConfigRepository.findByFlowChannel(
            flowChannel
        ).orElseThrow(() -> new BizException(ResultCode.NO_LIMIT_DAY_LOAN_FLOW_CHANNEL));
    }

    private void performLvXinCheck(ProjectLimitConfig config, BigDecimal amount, String prefix, String businessId) {
        if (config.getCreditDayLimit().compareTo(BigDecimal.ZERO) <= 0) {
            logger.warn("额度校验失败: [{}] 对应的项目已禁用或日限额为0。", businessId);
            throw new BizException(ResultCode.BIZ_ERROR);
        }
        String baseKey = buildLvXinLimitRedisKey(prefix, config);
        String valueKey = baseKey;
        String lockKey = baseKey + ":lock";
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.warn("额度校验失败: [{}] 获取额度锁 [{}] 超时。", businessId, lockKey);
                throw new BizException(ResultCode.BIZ_ERROR);
            }
            BigDecimal dayLimit = PREFIX_CREDIT.equals(prefix) ? config.getCreditDayLimit() : config.getLoanDayLimit();

            Object limitAmtObj = cacheService.get(valueKey);
            BigDecimal remainingAmt;
            if (limitAmtObj == null) {
                logger.info("[{}] 缓存未命中，开始从DB加载已使用金额. BusinessId: {}, CacheKey: {}", businessId, valueKey);
                BigDecimal usedAmount = getUsedAmountFromDB(prefix, config);
                logger.info("[{}] 从DB获取到的已使用金额 usedAmount: {}. BusinessId: {}", usedAmount, businessId);
                remainingAmt = dayLimit.subtract(usedAmount);
                logger.info("[{}] 缓存为空，初始化后的剩余额度 remainingAmt: {}. BusinessId: {}", remainingAmt, businessId);
            } else {
                remainingAmt = new BigDecimal(String.valueOf(limitAmtObj));
                logger.info("[{}] 缓存命中，当前剩余额度 remainingAmt: {}. BusinessId: {}, CacheKey: {}", remainingAmt, businessId, valueKey);
            }
            if (remainingAmt.compareTo(amount) < 0) {
                logger.warn("Redis缓存额度不足，开始从DB重新计算进行二次确认. BusinessId: [{}], 剩余: [{}], 需要: [{}]",
                    businessId, remainingAmt, amount);
                BigDecimal usedAmountFromDB = getUsedAmountFromDB(prefix, config);
                remainingAmt = dayLimit.subtract(usedAmountFromDB);
                logger.info("二次确认：从DB计算出的最新剩余额度为: [{}]. BusinessId: [{}]", remainingAmt, businessId);
            }

            if (remainingAmt.compareTo(amount) < 0) {
                logger.warn("额度校验失败: 经DB二次确认后额度仍然不足. BusinessId: [{}], 最终剩余: [{}], 需: [{}]",
                    businessId, remainingAmt, amount);
                cacheService.put(valueKey, remainingAmt.max(BigDecimal.ZERO), 24, TimeUnit.HOURS);
                throw new BizException(ResultCode.NO_LIMIT_DAY_LOAN_FLOW_CHANNEL);
            }

            BigDecimal newRemainingAmt = remainingAmt.subtract(amount);
            cacheService.put(valueKey, newRemainingAmt, 24, TimeUnit.HOURS);
            logger.info("额度校验通过: 扣减成功. BusinessId: [{}], 剩余额度更新为: [{}], 原额度: [{}], 本次扣减: [{}]",
                businessId, newRemainingAmt, remainingAmt, amount);

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logger.error("{}额度校验时发生未知异常, BusinessId: {}", prefix, businessId, e);
            throw new BizException(ResultCode.CREDIT_ERROR);
        } finally {
            lock.unlock();

        }
    }


    private void performPaiPaiCheck(ProjectLimitConfig config, BigDecimal amount, String prefix, String businessId) {
        if (config.getCreditDayLimit().compareTo(BigDecimal.ZERO) <= 0) {
            logger.warn("额度校验失败: [{}] 对应的项目已禁用或日限额为0。", businessId);
            throw new BizException(ResultCode.BIZ_ERROR);
        }

        String baseKey = buildPaiPaiLimitRedisKey(prefix, config);
        String valueKey = baseKey;
        String lockKey = baseKey + ":lock";
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.warn("额度校验失败: [{}] 获取额度锁 [{}] 超时。", businessId, lockKey);
                throw new BizException(ResultCode.BIZ_ERROR);
            }
            BigDecimal dayLimit = PREFIX_CREDIT.equals(prefix) ? config.getCreditDayLimit() : config.getLoanDayLimit();
            logger.info("额度检查开始. BusinessId: [{}], Key: [{}], 总限额: [{}]", businessId, valueKey, dayLimit);

            Object limitAmtObj = cacheService.get(valueKey);
            BigDecimal remainingAmt;
            if (limitAmtObj == null) {
                logger.info("缓存未命中，从DB加载已用额度进行初始化. BusinessId: [{}]", businessId);
                BigDecimal usedAmount = getUsedAmountFromDB(prefix, config);
                remainingAmt = dayLimit.subtract(usedAmount);
                logger.info("DB已用额度: [{}], 计算后初始剩余额度: [{}]. BusinessId: [{}]", usedAmount, remainingAmt, businessId);
            } else {
                remainingAmt = new BigDecimal(limitAmtObj.toString());
                logger.info("缓存命中，当前Redis剩余额度: [{}]. BusinessId: [{}]", remainingAmt, businessId);
            }


            if (remainingAmt.compareTo(amount) < 0) {
                logger.warn("Redis缓存额度不足，开始从DB重新计算进行二次确认. BusinessId: [{}], 剩余: [{}], 需要: [{}]",
                    businessId, remainingAmt, amount);
                BigDecimal usedAmountFromDB = getUsedAmountFromDB(prefix, config);
                remainingAmt = dayLimit.subtract(usedAmountFromDB);
                logger.info("二次确认：从DB计算出的最新剩余额度为: [{}]. BusinessId: [{}]", remainingAmt, businessId);
            }

            if (remainingAmt.compareTo(amount) < 0) {
                logger.warn("额度校验失败: 经DB二次确认后额度仍然不足. BusinessId: [{}], 最终剩余: [{}], 需: [{}]",
                    businessId, remainingAmt, amount);
                cacheService.put(valueKey, remainingAmt.max(BigDecimal.ZERO), 24, TimeUnit.HOURS);
                throw new BizException(ResultCode.NO_LIMIT_DAY_LOAN_FLOW_CHANNEL);
            }

            BigDecimal newRemainingAmt = remainingAmt.subtract(amount);
            cacheService.put(valueKey, newRemainingAmt, 24, TimeUnit.HOURS);
            logger.info("额度校验通过: 扣减成功. BusinessId: [{}], 剩余额度更新为: [{}], 原额度: [{}], 本次扣减: [{}]",
                businessId, newRemainingAmt, remainingAmt, amount);

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            logger.error("{}额度校验时发生未知异常, BusinessId: {}", prefix, businessId, e);
            throw new BizException(ResultCode.CREDIT_ERROR);
        } finally {
            lock.unlock();
        }
    }

    private void performSuspendCheck(Loan loan) {
        String flowChannelStr = loan.getFlowChannel().toString();
        String guaranteeCompStr = loan.getGuaranteeCompany().toString();
        String bankChannelStr = loan.getBankChannel().toString();
        Optional<ProjectLimitConfig> configOpt = projectLimitConfigRepository.findByFlowChannelAndGuaranteeCompAndBankChannel(
            flowChannelStr, guaranteeCompStr, bankChannelStr);

        if (configOpt.isEmpty()) {
            logger.warn("limitSuspend: [{}], 项目维度[{}|{}|{}|{}]未配置限额, 拒绝放款.",
                loan.getId(), flowChannelStr, guaranteeCompStr, bankChannelStr);
            throw new BizException(ResultCode.CREDIT_NOT_SUCCEED);
        }

        ProjectLimitConfig projectConfig = configOpt.get();

        if (projectConfig.getLoanDayLimit().compareTo(BigDecimal.ZERO) <= 0) {
            logger.info("limitSuspend: [{}], 项目已禁用或放款日限额为0, 挂起处理.", loan.getId());
            throw new BizException(ResultCode.CREDIT_NOT_SUCCEED);
        }

        String baseKey = buildLvXinLimitRedisKey(PREFIX_LOAN, projectConfig);
        String valueKey = baseKey;
        String lockKey = baseKey + ":lock";
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.info("limitSuspend: [{}], 未获取到额度锁 [{}], 本次不校验.", loan.getId(), lockKey);
                throw new BizException(ResultCode.CREDIT_NOT_SUCCEED);
            }
            BigDecimal loanDayLimit = projectConfig.getLoanDayLimit();
            Object limitAmtObj = cacheService.get(valueKey);
            BigDecimal remainingAmt;
            if (limitAmtObj == null) {
                logger.info("limitSuspend: [{}], Redis Key [{}] 不存在, 按日总限额 [{}] 处理.", loan.getId(), lockKey, loanDayLimit);
                remainingAmt = loanDayLimit;
            } else {
                remainingAmt = new BigDecimal(String.valueOf(limitAmtObj));
                logger.info("limitSuspend: [{}], Redis Key [{}], 当前剩余额度: {}", loan.getId(), valueKey, remainingAmt);

            }
            if (remainingAmt.compareTo(loan.getAmount()) < 0) {
                logger.info("limitSuspend: [{}], 当前额度 {} 不足, 从DB进行兜底校验.", loan.getId(), remainingAmt);
                BigDecimal usedAmount = getUsedAmountFromDB(PREFIX_LOAN, projectConfig);
                remainingAmt = loanDayLimit.subtract(usedAmount);
                logger.info("limitSuspend: [{}], DB计算后精确剩余额度: {}", loan.getId(), remainingAmt);
            }

            if (remainingAmt.compareTo(loan.getAmount()) < 0) {
                logger.warn("limitSuspend: [{}], 最终额度不足. 剩余:{}, 需:{}. 头寸不足.",
                    loan.getId(), remainingAmt, loan.getAmount());
                cacheService.put(valueKey, remainingAmt.max(BigDecimal.ZERO), 24, TimeUnit.HOURS);
                throw new BizException(ResultCode.NO_LIMIT_DAY_LOAN_FLOW_CHANNEL);
            } else {
                BigDecimal newRemainingAmt = remainingAmt.subtract(loan.getAmount());
                logger.info("limitSuspend: [{}], 额度充足. 剩余:{}, 需:{}. 允许放款. 更新后剩余额度: {}",
                    loan.getId(), remainingAmt, loan.getAmount(), newRemainingAmt);
                cacheService.put(valueKey, newRemainingAmt, 24, TimeUnit.HOURS);
            }

        } catch (Exception e) {
            logger.error("放款挂起校验异常:", e);
        } finally {
            lock.unlock();
        }
    }


    private BigDecimal getUsedAmountFromDB(String prefix, ProjectLimitConfig config) {
        LocalDate currentDate = LocalDate.now();
        LocalDateTime todayStart = LocalDateTime.of(currentDate, LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(currentDate, LocalTime.MAX);

        FlowChannel flowChannelEnum = FlowChannel.valueOf(config.getFlowChannel());
        GuaranteeCompany guaranteeCompEnum = GuaranteeCompany.valueOf(config.getGuaranteeComp());
        BankChannel bankChannelEnum = BankChannel.valueOf(config.getBankChannel());

        BigDecimal succeedAmt = BigDecimal.ZERO;
        if (PREFIX_LOAN.equals(prefix)) {
            succeedAmt = loanRepository.sumAmountByDimensionsAndStates(
                flowChannelEnum, guaranteeCompEnum, bankChannelEnum, ProcessState.SUCCEED,
                todayStart, todayEnd
            ).orElse(BigDecimal.ZERO);
        }else if (PREFIX_CREDIT.equals(prefix)) {
            succeedAmt = creditRepository.sumAmountByDimensionsAndStates(
                flowChannelEnum, guaranteeCompEnum, bankChannelEnum,
                ProcessState.SUCCEED,
                todayStart, todayEnd
            ).orElse(BigDecimal.ZERO);
        }
        return succeedAmt;
    }


    private String buildLvXinLimitRedisKey(String prefix, ProjectLimitConfig config) {
        return String.join(":",
            prefix,
            config.getFlowChannel(),
            config.getGuaranteeComp(),
            config.getBankChannel(),
            LocalDate.now().format(DATE_FORMATTER)
        );
    }

    private String buildPaiPaiLimitRedisKey(String prefix, ProjectLimitConfig config) {
        return String.join(":",
            prefix,
            config.getFlowChannel(),
            config.getBankChannel(),
            LocalDate.now().format(DATE_FORMATTER)
        );
    }
}
